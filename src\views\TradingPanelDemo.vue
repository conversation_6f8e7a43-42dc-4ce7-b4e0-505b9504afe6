<template>
  <div class="demo-container">
    <h2>期货交易面板演示</h2>
    <p>这是一个模拟的期货交易界面，展示了Windows 95风格的UI设计</p>
    
    <!-- 直接嵌入交易面板 -->
    <TradingPanel />
  </div>
</template>

<script setup lang="ts">
import TradingPanel from './TradingPanel.vue'
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100vh;
  background: #c0c0c0;
  padding: 10px;
  box-sizing: border-box;
}

h2 {
  color: #000000;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 14px;
  margin: 0 0 10px 0;
}

p {
  color: #000000;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  margin: 0 0 10px 0;
}
</style>
